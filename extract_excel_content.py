from excel_document_parser import ExcelDocumentParser

input_data = {
  "interface_name_arr": [
    "合作机构客户承接查询接口",
    "合作机构客户协议查询接口",
    "合作机构借款试算接口",
    "WB借款试算接口",
    "合作机构授信申请接口",
    "合作机构授信申请结论查询接口",
    "合作机构授信申请结论通知接口",
    "合作机构授信额度确认接口",
    "合作机构查询微众侧授信确认结果",
    "合作机构借款申请接口",
    "合作机构借款结论查询接口",
    "合作机构借款审批结论通知接口",
    "合作机构借款支付结果通知接口",
    "合作机构借款支付退汇通知接口",
    "合作机构还款申请接口",
    "合作机构还款结果通知接口",
    "合作机构还款结果查询接口",
    "合作机构客户信息查询接口",
    "合作机构客户借据列表查询接口",
    "合作机构客户借据详情查询接口",
    "合作机构客户借还记录查询",
    "合作机构客户已签约协议查询接口",
    "合作机构代扣签约查询",
    "合作机构代扣签约请求并触发OTP",
    "合作机构代扣签约验证OTP",
    "合作机构客户补充信息同步",
    "合作机构短信发送接口",
    "CAP-UP通用接口-联机拉取影像件"
  ],
  "common_structure_arr": [
    "业务公共报文"
  ],
  "enum_arr": [
    "数据字典"
  ],
  "file_url": "https://ai-studio-dev.daikuan.qihoo.net/files/57d48cf5-5d52-49aa-9f35-5f0bde7545d2/file-preview?timestamp=1751546784&nonce=dcfc0f322a9e5c91b447a8819050bb43&sign=oUx4USEYYQVZeDd26wO0muQxFKmgiEFo_CgYdQHTf1U="
}


def main(interface_name_arr: list, common_structure_arr: list, enum_arr: list, file_url: str) -> dict:
    excel_parser = ExcelDocumentParser(file_url)
    all_headings = excel_parser.get_all_headings()
    content_map = excel_parser.get_sections_document_blocks_dict(all_headings)
    interface_content_arr = []
    common_structure_content_arr = []
    enum_content_arr = []
    for interface_name in interface_name_arr:
        interface_content_arr.append(content_map[interface_name][0]['content'])
    for common_structure in common_structure_arr:
        common_structure_content_arr.append(content_map[common_structure][0]['content'])
    for enum in enum_arr:
        enum_content_arr.append(content_map[enum][0]['content'])
    return {
        "interface_content_arr": interface_content_arr,
        "common_structure_content_arr": common_structure_content_arr,
        "enum_content_arr": enum_content_arr
    }

main(input_data['interface_name_arr'],input_data['common_structure_arr'],input_data['enum_arr'],input_data['file_url'])
