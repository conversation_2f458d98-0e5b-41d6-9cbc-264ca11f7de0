from word_document_parser import WordDocumentParser

input_data = {
  "interface_name_arr": [
    "借贷接口规范 V1",
    "准入与授信管理接口 (借贷平台提供)",
    "0101 – 用户准入接口(account.check)",
    "0102 – 授信检查接口(credit.check)",
    "0103 – 授信确认接口(credit.confirm)",
    "0104 – 授信结果查询接口(credit.status.query)",
    "0105 – 授信关系查询接口(credit.account.query)",
    "0106 – 用户开具证明接口(account.settlement.query)",
    "0107 – 发送开具证明接口(account.settlement.send)",
    "0108 – 更换手机号接口(account.phoneno.change)",
    "0109 – 活体人脸校验接口(face.check)",
    "0110 – 身份证上传接口(eid.upload)",
    "0111 – 联系人添加接口(contact.add)",
    "0112 – 个人资料补充接口(userinfo.supplement)",
    "0113 – 销户检查(account.cancel.check)",
    "0114 – 生命周期操作(account.lifecycle)",
    "0115 –身份证查询操作(eid.upload.query)",
    "0116 –短信发送（otp.apply）",
    "用信接口(借贷平台提供)",
    "0201 - 默认试算（borrow.trial.default）",
    "0202 - 借贷试算（borrow.trial）",
    "0203 – 创建订单（borrow.apply）",
    "0204 – 订单确认（borrow.confirm）",
    "0205 – offer变更确认（borrow.offer.confirm）",
    "0206 - 查询用信申请结果（borrow.status.query）",
    "0207 - 查询借钱记录（borrow.order.query）",
    "0208 – 取消未完成订单（borrow.order.cancel）",
    "0209 – 查询借款订单详情（borrow.order.detail）",
    "还款接口(借贷平台提供)",
    "0301 - 查询月账单/所有借贷（repay.plan.query）",
    "0302 – 提前还款试算（repay.trial）",
    "0303 - 确认还款（repay.confirm）",
    "0304 - 查询还款结果（repay.status.query）",
    "0305 - 查询还款记录（repay.order.query）",
    "0306 - 查询还款记录详情（repay.order.detail.query）",
    "绑卡接口(借贷平台提供)",
    "0401 - 查询支持的银行列表（bank.support.list）[公开]",
    "0402 - 请求绑卡（bindcard.apply）",
    "0403 - 绑卡确认（bindcard.confirm）",
    "0404 - 用户自动还款设置（account.repay.set）",
    "0405 - 绑卡查询（bindcard.query）",
    "0406 – 删除绑定卡（bindcard.delete）",
    "0407 - 卡Bin校验（card.bin.check）",
    "回调接口（华为提供）",
    "0501 - 通知借贷结果（order.notify）",
    "0602 - 通知还款结果（repay.notify）",
    "0603 - 通知授信结果（credit.status.notify）",
    "0604 – 推送优惠券（coupon.send）",
    "0605 – 调额通知（credit.change.notify）",
    "0606 – 营销通知（promotion.notify）",
    "0607 – 手机号修改通知（phoneno.change.notify）",
    "0608 – 销户回调通知（account.cancel.notify）",
    "优惠券",
    "0701 – 查询优惠券（coupon.query）",
    "其他",
    "0801 – 客服凭证查询（sessionid.query）"
  ],
  "common_structure_arr": [
    "报文结构说明",
    "报文结构",
    "请求消息",
    "公共数据对象",
    "UserInfo 用户信息",
    "ImageInfo 身份证影像信息",
    "FaceImageInfo 活体人脸信息",
    "ContactInfo 联系人信息",
    "OtpInfo 短信信息",
    "CreditSignInfo 授信签约关系",
    "RiskInfo（风控信息）",
    "RepayTypeInfo（还款方式宣传信息）",
    "RepayFqInfo（分期宣传信息）",
    "RepayPlanInfo（还款计划信息）",
    "BorrowOrderInfo（借据信息）",
    "RepayTrialInfo（提前还款试算订单信息）",
    "RepayOrderInfo（还款记录）",
    "RepayOrderDetail（还款订单详情）",
    "BankInfo（银行信息）",
    "CardInfo（绑卡信息）",
    "CouponInfo（优惠券信息）",
    "ChangeInfo（授信内容变化信息）",
    "AddressInfo（地址信息）",
    "NotifyInfo（营销推广信息）"
  ],
  "enum_arr": ["公共错误码"],
  "file_url": "https://ai-studio-dev.daikuan.qihoo.net/files/8eab26cc-5afc-4abe-bf56-f84b89d7d216/file-preview?timestamp=**********&nonce=35983e1a78f3612d4dfc7b72dd0a0e31&sign=79OwFkwZtffpPP0g9ZUMIUJu4eCxZ0uoJziNwa_YaPU="
}

def get_content(content_name_arr: list, content_map: dict):
    content_arr = []
    char_count = 0
    content = ""
    pre_char_count = 0
    pre_content = ""
    for content_name in content_name_arr:
        if char_count > 0:
            pre_char_count = char_count
            pre_content = content
            char_count = 0
            content = ""
        content_detail_arr = content_map[content_name]
        for content_detail in content_detail_arr:
            char_count += content_detail["char_count"]
            content += "\n\n\n" + content_detail["content"]
            if char_count > 2000:
                if pre_char_count > 0:
                    content_arr.append(pre_content)
                    pre_char_count = 0
                    pre_content = ""
                content_arr.append(content)
                char_count = 0
                content = ""

        char_count = pre_char_count + char_count
        content = pre_content + "\n\n\n" + content
        pre_char_count = 0
        pre_content = ""
        if char_count > 2000:
            content_arr.append(content)
            char_count = 0
            content = ""
    if char_count > 0:
        content_arr.append(content)
    return content_arr

def main(interface_name_arr: list, common_structure_arr: list, enum_arr: list, file_url: str) -> dict:
    word_parser = WordDocumentParser(file_url)
    all_headings_list = interface_name_arr + common_structure_arr + enum_arr
    content_map = word_parser.get_headings_document_blocks_dict(all_headings_list)
    interface_content_arr = get_content(interface_name_arr, content_map)
    common_structure_content_arr = get_content(common_structure_arr, content_map)
    enum_content_arr = get_content(enum_arr, content_map)
    return {
        "interface_content_arr": interface_content_arr,
        "common_structure_content_arr": common_structure_content_arr,
        "enum_content_arr": enum_content_arr
    }

main(input_data['interface_name_arr'],input_data['common_structure_arr'],input_data['enum_arr'],input_data['file_url'])