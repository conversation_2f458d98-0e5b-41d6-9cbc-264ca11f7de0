import json
import requests

def fetch_data_from_api(url: str, authorization: str, payload: dict):
    try:
        # 定义请求头（示例）
        headers = {
            "Content-Type": "application/json",  # 声明请求体格式
            "Authorization": authorization,  # 身份验证令牌
        }
        # 发送 GET 请求
        response = requests.post(url, None, payload, headers=headers)

        # 检查请求是否成功（HTTP 200）
        if response.status_code == 200:
            # 解析 JSON 数据
            json_data = response.json()

            # 检查返回的数据结构是否符合预期
            if isinstance(json_data, dict) and "data" in json_data:
                data = json_data["data"]
                return data
            else:
                return None
        else:
            return None
    except requests.exceptions.RequestException as e:
        return None

def save_schema_info(api_url: str, authorization: str, document_name: str, suffix:str, partner_code: str, partner_desc: str, schema_info: dict, type: str, force_update):
    name = schema_info["name"]
    desc = schema_info["desc"]
    if "link_common_structures" in schema_info:
        link_common_structures = schema_info["link_common_structures"]
    else:
        link_common_structures = []

    if "link_enums" in schema_info:
        link_enums = schema_info["link_enums"]
    else:
        link_enums = []
    link_schema_info = {
        "link_common_structures": link_common_structures,
        "link_enums": link_enums
    }
    content = json.dumps(schema_info, ensure_ascii=False)
    link_schema_info_str = json.dumps(link_schema_info, ensure_ascii=False)
    if name and type and partner_code and partner_desc:
        payload = {
            "documentName": document_name,
            "suffix": suffix,
            "partnerCode": partner_code,
            "partnerDesc": partner_desc,
            "name": name,
            "desc": desc,
            "type": type,
            "content": content,
            "linkSchemaInfo": link_schema_info_str,
            "forceUpdate": force_update == 'Y'
        }
        fetch_data_from_api(api_url, authorization, payload)

def main(result: str, document_name: str, suffix:str, partner_code: str, partner_desc: str, force_update: str, tip_domain_name:str, authorization: str) -> dict:
    api_url = tip_domain_name + "/agent/api/partner/schema/save"
    json_str = result.replace('```json\n', ' ').replace('\n```', ' ').replace('\n', ' ').strip()
    data = json.loads(json_str)
    interfaces = data["interfaces"]
    for interface in interfaces:
        save_schema_info(api_url, authorization, document_name, suffix, partner_code, partner_desc, interface, "interface", force_update)

    common_structures = data["common_structures"]
    for common_structure in common_structures:
        save_schema_info(api_url, authorization, document_name, suffix, partner_code, partner_desc, common_structure, "common_structure", force_update)

    enums = data["enums"]
    for enum in enums:
        save_schema_info(api_url, authorization, document_name, suffix, partner_code, partner_desc, enum, "enum", force_update)
    return {
        "result": data,
    }
