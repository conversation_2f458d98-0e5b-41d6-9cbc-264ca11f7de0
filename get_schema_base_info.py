import requests
import json


def fetch_data_from_api(url: str, authorization: str, partner_code: str, type: str):
    try:
        # 定义请求头（示例）
        headers = {
            "Content-Type": "application/json",  # 声明请求体格式
            "Authorization": authorization,  # 身份验证令牌
        }

        params = {
            "partnerCode": partner_code,
            "type": type
        }
        # 发送 GET 请求
        response = requests.get(url, headers=headers, params=params)

        # 检查请求是否成功（HTTP 200）
        if response.status_code == 200:
            # 解析 JSON 数据
            json_data = response.json()

            # 检查返回的数据结构是否符合预期
            if isinstance(json_data, dict) and "data" in json_data:
                data = json_data["data"]
                return data
            else:
                return None
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None


def main(tip_domain_name: str, authorization: str, partner_code: str, type: str) -> dict:
    api_url = tip_domain_name + "/agent/api/partner/schema/base/info/bytype"
    # 调用函数获取数据
    data_content = fetch_data_from_api(api_url, authorization, partner_code, type)
    if data_content:
        return {
            "schema_base_info": json.dumps(data_content, ensure_ascii=False),
        }
    else:
        return {
            "schema_base_info": None,
        }


stg1_url = "http://**************"
stg3_url = "http://**************"

if __name__ == '__main__':
    print(main(stg3_url, "c462d9b14217489a84c70df2bcca6d62", "HuaWei", "interface"))